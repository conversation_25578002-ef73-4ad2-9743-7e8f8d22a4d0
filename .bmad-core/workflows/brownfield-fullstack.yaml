# <!-- Powered by BMAD™ Core -->
workflow:
  id: brownfield-fullstack
  name: Brownfield Full-Stack Enhancement
  description: >-
    Agent workflow for enhancing existing full-stack applications with new features,
    modernization, or significant changes. Handles existing system analysis and safe integration.
  type: brownfield
  project_types:
    - feature-addition
    - refactoring
    - modernization
    - integration-enhancement

  sequence:
    - step: enhancement_classification
      agent: analyst
      action: classify enhancement scope
      notes: |
        Determine enhancement complexity to route to appropriate path:
        - Single story (< 4 hours) → Use brownfield-create-story task
        - Small feature (1-3 stories) → Use brownfield-create-epic task  
        - Major enhancement (multiple epics) → Continue with full workflow

        Ask user: "Can you describe the enhancement scope? Is this a small fix, a feature addition, or a major enhancement requiring architectural changes?"

    - step: routing_decision
      condition: based_on_classification
      routes:
        single_story:
          agent: pm
          uses: brownfield-create-story
          notes: "Create single story for immediate implementation. Exit workflow after story creation."
        small_feature:
          agent: pm
          uses: brownfield-create-epic
          notes: "Create focused epic with 1-3 stories. Exit workflow after epic creation."
        major_enhancement:
          continue: to_next_step
          notes: "Continue with comprehensive planning workflow below."

    - step: documentation_check
      agent: analyst
      action: check existing documentation
      condition: major_enhancement_path
      notes: |
        Check if adequate project documentation exists:
        - Look for existing architecture docs, API specs, coding standards
        - Assess if documentation is current and comprehensive
        - If adequate: Skip document-project, proceed to PRD
        - If inadequate: Run document-project first

    - step: project_analysis
      agent: architect
      action: analyze existing project and use task document-project
      creates: brownfield-architecture.md (or multiple documents)
      condition: documentation_inadequate
      notes: "Run document-project to capture current system state, technical debt, and constraints. Pass findings to PRD creation."

    - agent: pm
      creates: prd.md
      uses: brownfield-prd-tmpl
      requires: existing_documentation_or_analysis
      notes: |
        Creates PRD for major enhancement. If document-project was run, reference its output to avoid re-analysis.
        If skipped, use existing project documentation.
        SAVE OUTPUT: Copy final prd.md to your project's docs/ folder.

    - step: architecture_decision
      agent: pm/architect
      action: determine if architecture document needed
      condition: after_prd_creation
      notes: |
        Review PRD to determine if architectural planning is needed:
        - New architectural patterns → Create architecture doc
        - New libraries/frameworks → Create architecture doc
        - Platform/infrastructure changes → Create architecture doc
        - Following existing patterns → Skip to story creation

    - agent: architect
      creates: architecture.md
      uses: brownfield-architecture-tmpl
      requires: prd.md
      condition: architecture_changes_needed
      notes: "Creates architecture ONLY for significant architectural changes. SAVE OUTPUT: Copy final architecture.md to your project's docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all documents for integration safety and completeness. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - agent: po
      action: shard_documents
      creates: sharded_docs
      requires: all_artifacts_in_project
      notes: |
        Shard documents for IDE development:
        - Option A: Use PO agent to shard: @po then ask to shard docs/prd.md
        - Option B: Manual: Drag shard-doc task + docs/prd.md into chat
        - Creates docs/prd/ and docs/architecture/ folders with sharded content

    - agent: sm
      action: create_story
      creates: story.md
      requires: sharded_docs_or_brownfield_docs
      repeats: for_each_epic_or_enhancement
      notes: |
        Story creation cycle:
        - For sharded PRD: @sm → *create (uses create-next-story)
        - For brownfield docs: @sm → use create-brownfield-story task
        - Creates story from available documentation
        - Story starts in "Draft" status
        - May require additional context gathering for brownfield

    - agent: analyst/pm
      action: review_draft_story
      updates: story.md
      requires: story.md
      optional: true
      condition: user_wants_story_review
      notes: |
        OPTIONAL: Review and approve draft story
        - NOTE: story-review task coming soon
        - Review story completeness and alignment
        - Update story status: Draft → Approved

    - agent: dev
      action: implement_story
      creates: implementation_files
      requires: story.md
      notes: |
        Dev Agent (New Chat): @dev
        - Implements approved story
        - Updates File List with all changes
        - Marks story as "Review" when complete

    - agent: qa
      action: review_implementation
      updates: implementation_files
      requires: implementation_files
      optional: true
      notes: |
        OPTIONAL: QA Agent (New Chat): @qa → review-story
        - Senior dev review with refactoring ability
        - Fixes small issues directly
        - Leaves checklist for remaining items
        - Updates story status (Review → Done or stays Review)

    - agent: dev
      action: address_qa_feedback
      updates: implementation_files
      condition: qa_left_unchecked_items
      notes: |
        If QA left unchecked items:
        - Dev Agent (New Chat): Address remaining items
        - Return to QA for final approval

    - repeat_development_cycle:
      action: continue_for_all_stories
      notes: |
        Repeat story cycle (SM → Dev → QA) for all epic stories
        Continue until all stories in PRD are complete

    - agent: po
      action: epic_retrospective
      creates: epic-retrospective.md
      condition: epic_complete
      optional: true
      notes: |
        OPTIONAL: After epic completion
        - NOTE: epic-retrospective task coming soon
        - Validate epic was completed correctly
        - Document learnings and improvements

    - workflow_end:
      action: project_complete
      notes: |
        All stories implemented and reviewed!
        Project development phase complete.

        Reference: .bmad-core/data/bmad-kb.md#IDE Development Workflow

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: Brownfield Enhancement] --> B[analyst: classify enhancement scope]
        B --> C{Enhancement Size?}
        
        C -->|Single Story| D[pm: brownfield-create-story]
        C -->|1-3 Stories| E[pm: brownfield-create-epic]
        C -->|Major Enhancement| F[analyst: check documentation]
        
        D --> END1[To Dev Implementation]
        E --> END2[To Story Creation]
        
        F --> G{Docs Adequate?}
        G -->|No| H[architect: document-project]
        G -->|Yes| I[pm: brownfield PRD]
        H --> I
        
        I --> J{Architecture Needed?}
        J -->|Yes| K[architect: architecture.md]
        J -->|No| L[po: validate artifacts]
        K --> L
        
        L --> M{PO finds issues?}
        M -->|Yes| N[Fix issues]
        M -->|No| O[po: shard documents]
        N --> L
        
        O --> P[sm: create story]
        P --> Q{Story Type?}
        Q -->|Sharded PRD| R[create-next-story]
        Q -->|Brownfield Docs| S[create-brownfield-story]
        
        R --> T{Review draft?}
        S --> T
        T -->|Yes| U[review & approve]
        T -->|No| V[dev: implement]
        U --> V
        
        V --> W{QA review?}
        W -->|Yes| X[qa: review]
        W -->|No| Y{More stories?}
        X --> Z{Issues?}
        Z -->|Yes| AA[dev: fix]
        Z -->|No| Y
        AA --> X
        Y -->|Yes| P
        Y -->|No| AB{Retrospective?}
        AB -->|Yes| AC[po: retrospective]
        AB -->|No| AD[Complete]
        AC --> AD

        style AD fill:#90EE90
        style END1 fill:#90EE90
        style END2 fill:#90EE90
        style D fill:#87CEEB
        style E fill:#87CEEB
        style I fill:#FFE4B5
        style K fill:#FFE4B5
        style O fill:#ADD8E6
        style P fill:#ADD8E6
        style V fill:#ADD8E6
        style U fill:#F0E68C
        style X fill:#F0E68C
        style AC fill:#F0E68C
    ```

  decision_guidance:
    when_to_use:
      - Enhancement requires coordinated stories
      - Architectural changes are needed
      - Significant integration work required
      - Risk assessment and mitigation planning necessary
      - Multiple team members will work on related changes

  handoff_prompts:
    classification_complete: |
      Enhancement classified as: {{enhancement_type}}
      {{if single_story}}: Proceeding with brownfield-create-story task for immediate implementation.
      {{if small_feature}}: Creating focused epic with brownfield-create-epic task.
      {{if major_enhancement}}: Continuing with comprehensive planning workflow.

    documentation_assessment: |
      Documentation assessment complete:
      {{if adequate}}: Existing documentation is sufficient. Proceeding directly to PRD creation.
      {{if inadequate}}: Running document-project to capture current system state before PRD.

    document_project_to_pm: |
      Project analysis complete. Key findings documented in:
      - {{document_list}}
      Use these findings to inform PRD creation and avoid re-analyzing the same aspects.

    pm_to_architect_decision: |
      PRD complete and saved as docs/prd.md. 
      Architectural changes identified: {{yes/no}}
      {{if yes}}: Proceeding to create architecture document for: {{specific_changes}}
      {{if no}}: No architectural changes needed. Proceeding to validation.

    architect_to_po: "Architecture complete. Save it as docs/architecture.md. Please validate all artifacts for integration safety."

    po_to_sm: |
      All artifacts validated. 
      Documentation type available: {{sharded_prd / brownfield_docs}}
      {{if sharded}}: Use standard create-next-story task.
      {{if brownfield}}: Use create-brownfield-story task to handle varied documentation formats.

    sm_story_creation: |
      Creating story from {{documentation_type}}.
      {{if missing_context}}: May need to gather additional context from user during story creation.

    complete: "All planning artifacts validated and development can begin. Stories will be created based on available documentation format."
